<?php
// Hospital Patient Barcode Generator
session_start();
require('fpdf/fpdf.php');

// Function to generate barcode using Code128
function generateBarcode($text, $width = 1.5, $height = 30) {
    // Simple barcode generation using HTML/CSS bars
    // This creates a visual representation - for production use a proper barcode library
    $barcode = '';
    $text = strtoupper($text);

    // Simple encoding for demonstration
    $bars = [];
    for ($i = 0; $i < strlen($text); $i++) {
        $char = ord($text[$i]);
        // Create alternating bar pattern based on character ASCII value
        $pattern = str_pad(decbin($char % 128), 7, '0', STR_PAD_LEFT);
        for ($j = 0; $j < 7; $j++) {
            $bars[] = $pattern[$j] == '1' ? 1 : 0;
        }
    }

    // Generate HTML bars for ID card
    $barcode = '<div class="id-barcode">';
    foreach ($bars as $bar) {
        $color = $bar ? '#000' : '#fff';
        $barcode .= '<div style="display: inline-block; width: ' . $width . 'px; height: ' . $height . 'px; background-color: ' . $color . ';"></div>';
    }
    $barcode .= '<div class="barcode-text">' . $text . '</div>';
    $barcode .= '</div>';

    return $barcode;
}

// Function to generate barcode bars array for PDF
function generateBarcodeArray($text) {
    $text = strtoupper($text);
    $bars = [];

    for ($i = 0; $i < strlen($text); $i++) {
        $char = ord($text[$i]);
        // Create alternating bar pattern based on character ASCII value
        $pattern = str_pad(decbin($char % 128), 7, '0', STR_PAD_LEFT);
        for ($j = 0; $j < 7; $j++) {
            $bars[] = $pattern[$j] == '1' ? 1 : 0;
        }
    }

    return $bars;
}

// Custom PDF class for ID Card
class IDCardPDF extends FPDF {
    function IDCard($patient_data) {
        // A4 size: 210 x 297 mm
        // 1/4 of A4 landscape: 148.5 x 105 mm (landscape orientation)
        $this->AddPage('L', array(148.5, 105));

        // Set margins
        $this->SetMargins(5, 5, 5);

        // Background gradient effect (using rectangles)
        $this->SetFillColor(102, 126, 234); // Blue
        $this->Rect(0, 0, 148.5, 105, 'F');

        $this->SetFillColor(118, 75, 162); // Purple
        $this->Rect(74.25, 0, 74.25, 105, 'F');

        // White overlay for content area
        $this->SetFillColor(255, 255, 255);
        $this->SetAlpha(0.1);
        $this->Rect(2, 2, 144.5, 101, 'F');
        $this->SetAlpha(1);

        // Hospital Header
        $this->SetTextColor(255, 255, 255);
        $this->SetFont('Arial', 'B', 12);
        $this->SetXY(5, 8);
        $this->Cell(138.5, 8, 'GENERAL HOSPITAL', 0, 1, 'C');

        $this->SetFont('Arial', '', 8);
        $this->SetXY(5, 16);
        $this->Cell(138.5, 5, 'PATIENT IDENTIFICATION CARD', 0, 1, 'C');

        // Photo placeholder
        $this->SetFillColor(255, 255, 255);
        $this->SetDrawColor(200, 200, 200);
        $this->Rect(10, 25, 25, 25, 'FD');

        $this->SetTextColor(100, 100, 100);
        $this->SetFont('Arial', '', 6);
        $this->SetXY(10, 35);
        $this->Cell(25, 3, '1x1 PHOTO', 0, 1, 'C');
        $this->SetXY(10, 38);
        $this->Cell(25, 3, 'HERE', 0, 1, 'C');

        // Patient Information
        $this->SetTextColor(255, 255, 255);
        $this->SetFont('Arial', 'B', 8);

        // Name
        $this->SetXY(40, 25);
        $this->Cell(30, 4, 'PATIENT NAME:', 0, 0, 'L');
        $this->SetFont('Arial', '', 9);
        $this->SetXY(40, 30);
        $this->Cell(100, 5, strtoupper($patient_data['name']), 0, 1, 'L');

        // Address
        $this->SetFont('Arial', 'B', 8);
        $this->SetXY(40, 37);
        $this->Cell(30, 4, 'ADDRESS:', 0, 0, 'L');
        $this->SetFont('Arial', '', 8);
        $this->SetXY(40, 42);
        $this->MultiCell(100, 4, strtoupper($patient_data['address']), 0, 'L');

        // Birth Date
        $this->SetFont('Arial', 'B', 8);
        $this->SetXY(40, 55);
        $this->Cell(30, 4, 'BIRTH DATE:', 0, 0, 'L');
        $this->SetFont('Arial', '', 8);
        $this->SetXY(40, 60);
        $this->Cell(50, 4, strtoupper(date('M j, Y', strtotime($patient_data['birthdate']))), 0, 0, 'L');

        // Hospital Number
        $this->SetFont('Arial', 'B', 8);
        $this->SetXY(95, 55);
        $this->Cell(30, 4, 'HOSPITAL NO.:', 0, 0, 'L');
        $this->SetFont('Arial', '', 8);
        $this->SetXY(95, 60);
        $this->Cell(40, 4, strtoupper($patient_data['hospital_number']), 0, 0, 'L');

        // Barcode section
        $this->drawBarcode($patient_data['hospital_number'], 40, 75);
    }

    function drawBarcode($text, $x, $y) {
        $bars = generateBarcodeArray($text);
        $barWidth = 0.8;
        $barHeight = 15;

        // White background for barcode
        $this->SetFillColor(255, 255, 255);
        $this->Rect($x - 2, $y - 2, (count($bars) * $barWidth) + 4, $barHeight + 8, 'F');

        // Draw bars
        $currentX = $x;
        foreach ($bars as $bar) {
            if ($bar == 1) {
                $this->SetFillColor(0, 0, 0); // Black bar
                $this->Rect($currentX, $y, $barWidth, $barHeight, 'F');
            }
            $currentX += $barWidth;
        }

        // Barcode text
        $this->SetTextColor(0, 0, 0);
        $this->SetFont('Arial', '', 8);
        $this->SetXY($x, $y + $barHeight + 2);
        $this->Cell(count($bars) * $barWidth, 4, strtoupper($text), 0, 0, 'C');
    }

    function SetAlpha($alpha) {
        // Set transparency (simplified version)
        $this->_out(sprintf('/GS1 gs'));
    }
}

// Process form submission
$patient_data = [];
$barcode_html = '';
$error_message = '';

// Handle PDF generation
if (isset($_GET['pdf']) && $_GET['pdf'] == '1' && isset($_SESSION['patient_data'])) {
    $pdf = new IDCardPDF();
    $pdf->IDCard($_SESSION['patient_data']);
    $pdf->Output('D', 'patient_id_card.pdf');
    exit;
}

if ($_POST) {
    $name = trim($_POST['name'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $birthdate = trim($_POST['birthdate'] ?? '');
    $hospital_number = trim($_POST['hospital_number'] ?? '');

    // Validation
    if (empty($name) || empty($address) || empty($birthdate) || empty($hospital_number)) {
        $error_message = 'All fields are required.';
    } elseif (!preg_match('/^[A-Za-z0-9]+$/', $hospital_number)) {
        $error_message = 'Hospital number should contain only letters and numbers.';
    } else {
        $patient_data = [
            'name' => $name,
            'address' => $address,
            'birthdate' => $birthdate,
            'hospital_number' => $hospital_number
        ];

        // Store in session for PDF generation
        $_SESSION['patient_data'] = $patient_data;

        // Generate barcode for hospital number
        $barcode_html = generateBarcode($hospital_number);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hospital Patient Barcode Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }

        input[type="text"], input[type="date"], textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }

        input[type="text"]:focus, input[type="date"]:focus, textarea:focus {
            border-color: #3498db;
            outline: none;
        }

        textarea {
            height: 80px;
            resize: vertical;
        }

        .btn {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .error {
            background-color: #e74c3c;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        /* ID Card Styles */
        .id-card-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            padding: 20px;
        }

        .id-card {
            width: 400px;
            height: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
            color: white;
            font-family: 'Arial', sans-serif;
        }

        .id-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }

        .hospital-header {
            position: absolute;
            top: 8px;
            left: 15px;
            right: 15px;
            text-align: center;
            z-index: 2;
        }

        .hospital-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .card-type {
            font-size: 10px;
            margin: 2px 0 0 0;
            opacity: 0.9;
        }

        .card-content {
            display: flex;
            padding: 55px 15px 15px 15px;
            height: calc(100% - 70px);
            gap: 15px;
        }

        .photo-section {
            flex-shrink: 0;
        }

        .photo-placeholder {
            width: 80px;
            height: 80px;
            background-color: rgba(255,255,255,0.9);
            border: 2px solid rgba(255,255,255,0.5);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 10px;
            text-align: center;
            line-height: 1.2;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .info-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .patient-details {
            flex: 1;
        }

        .detail-row {
            margin-bottom: 8px;
            font-size: 11px;
            line-height: 1.3;
        }

        .detail-label {
            font-weight: bold;
            opacity: 0.8;
            font-size: 9px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 12px;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        .barcode-section {
            margin-top: 10px;
            text-align: center;
        }

        .id-barcode {
            background-color: white;
            padding: 3px;
            border-radius: 3px;
            display: inline-block;
            margin-bottom: 3px;
        }

        .barcode-text {
            font-family: 'Courier New', monospace;
            font-size: 8px;
            color: #333;
            text-align: center;
            margin-top: 2px;
            font-weight: bold;
        }

        .print-btn {
            background-color: #27ae60;
            margin-top: 20px;
            width: auto;
            padding: 10px 20px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .print-btn:hover {
            background-color: #229954;
        }

        .pdf-btn {
            background-color: #e74c3c;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
            width: auto;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }

        .pdf-btn:hover {
            background-color: #c0392b;
        }

        @media print {
            body {
                background-color: white;
                margin: 0;
                padding: 0;
            }
            .container {
                box-shadow: none;
                padding: 0;
            }
            form {
                display: none;
            }
            .print-btn {
                display: none;
            }
            h1 {
                display: none;
            }
            .id-card-container {
                margin: 0;
                padding: 0;
                justify-content: flex-start;
            }
            .id-card {
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Hospital Patient Barcode Generator</h1>

        <?php if ($error_message): ?>
            <div class="error">
                ⚠️ <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="form-group">
                <label for="name">Patient Name:</label>
                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="address">Address:</label>
                <textarea id="address" name="address" required><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
            </div>

            <div class="form-group">
                <label for="birthdate">Birth Date:</label>
                <input type="date" id="birthdate" name="birthdate" value="<?php echo htmlspecialchars($_POST['birthdate'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="hospital_number">Hospital Number:</label>
                <input type="text" id="hospital_number" name="hospital_number" value="<?php echo htmlspecialchars($_POST['hospital_number'] ?? ''); ?>" placeholder="e.g., H123456" required>
                <small style="color: #7f8c8d;">Only letters and numbers allowed</small>
            </div>

            <button type="submit" class="btn">🔄 Generate Barcode</button>
        </form>

        <?php if (!empty($patient_data) && !empty($barcode_html)): ?>
            <div class="id-card-container">
                <div class="id-card">
                    <div class="hospital-header">
                        <h3 class="hospital-name">GENERAL HOSPITAL</h3>
                        <p class="card-type">PATIENT IDENTIFICATION CARD</p>
                    </div>

                    <div class="card-content">
                        <div class="photo-section">
                            <div class="photo-placeholder">
                                1x1<br>PHOTO<br>HERE
                            </div>
                        </div>

                        <div class="info-section">
                            <div class="patient-details">
                                <div class="detail-row">
                                    <div class="detail-label">Patient Name</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($patient_data['name']); ?></div>
                                </div>

                                <div class="detail-row">
                                    <div class="detail-label">Address</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($patient_data['address']); ?></div>
                                </div>

                                <div class="detail-row">
                                    <div class="detail-label">Birth Date</div>
                                    <div class="detail-value"><?php echo htmlspecialchars(date('M j, Y', strtotime($patient_data['birthdate']))); ?></div>
                                </div>

                                <div class="detail-row">
                                    <div class="detail-label">Hospital No.</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($patient_data['hospital_number']); ?></div>
                                </div>
                            </div>

                            <div class="barcode-section">
                                <?php echo $barcode_html; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button onclick="window.print()" class="btn print-btn" style="margin-right: 10px;">🖨️ Print Preview</button>
                <a href="?pdf=1" class="btn print-btn" style="background-color: #e74c3c; text-decoration: none; display: inline-block;">📄 Download PDF</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>