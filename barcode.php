<?php
// Hospital Patient Barcode Generator
session_start();

// Function to generate barcode using Code128
function generateBarcode($text, $width = 2, $height = 50) {
    // Simple barcode generation using HTML/CSS bars
    // This creates a visual representation - for production use a proper barcode library
    $barcode = '';
    $text = strtoupper($text);

    // Simple encoding for demonstration
    $bars = [];
    for ($i = 0; $i < strlen($text); $i++) {
        $char = ord($text[$i]);
        // Create alternating bar pattern based on character ASCII value
        $pattern = str_pad(decbin($char % 128), 7, '0', STR_PAD_LEFT);
        for ($j = 0; $j < 7; $j++) {
            $bars[] = $pattern[$j] == '1' ? 1 : 0;
        }
    }

    // Generate HTML bars
    $barcode = '<div class="barcode" style="display: inline-block; border: 1px solid #000; padding: 5px;">';
    foreach ($bars as $bar) {
        $color = $bar ? '#000' : '#fff';
        $barcode .= '<div style="display: inline-block; width: ' . $width . 'px; height: ' . $height . 'px; background-color: ' . $color . ';"></div>';
    }
    $barcode .= '<br><div style="text-align: center; font-family: monospace; font-size: 12px; margin-top: 5px;">' . $text . '</div>';
    $barcode .= '</div>';

    return $barcode;
}

// Process form submission
$patient_data = [];
$barcode_html = '';
$error_message = '';

if ($_POST) {
    $name = trim($_POST['name'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $birthdate = trim($_POST['birthdate'] ?? '');
    $hospital_number = trim($_POST['hospital_number'] ?? '');

    // Validation
    if (empty($name) || empty($address) || empty($birthdate) || empty($hospital_number)) {
        $error_message = 'All fields are required.';
    } elseif (!preg_match('/^[A-Za-z0-9]+$/', $hospital_number)) {
        $error_message = 'Hospital number should contain only letters and numbers.';
    } else {
        $patient_data = [
            'name' => $name,
            'address' => $address,
            'birthdate' => $birthdate,
            'hospital_number' => $hospital_number
        ];

        // Generate barcode for hospital number
        $barcode_html = generateBarcode($hospital_number);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hospital Patient Barcode Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }

        input[type="text"], input[type="date"], textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }

        input[type="text"]:focus, input[type="date"]:focus, textarea:focus {
            border-color: #3498db;
            outline: none;
        }

        textarea {
            height: 80px;
            resize: vertical;
        }

        .btn {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .error {
            background-color: #e74c3c;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .result {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin-top: 30px;
        }

        .patient-info {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .patient-info h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        .info-row {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            color: #34495e;
            display: inline-block;
            width: 150px;
        }

        .barcode-section {
            text-align: center;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            border: 2px dashed #3498db;
        }

        .barcode-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        .print-btn {
            background-color: #27ae60;
            margin-top: 15px;
            width: auto;
            padding: 10px 20px;
        }

        .print-btn:hover {
            background-color: #229954;
        }

        @media print {
            body { background-color: white; }
            .container { box-shadow: none; }
            form { display: none; }
            .print-btn { display: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Hospital Patient Barcode Generator</h1>

        <?php if ($error_message): ?>
            <div class="error">
                ⚠️ <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="form-group">
                <label for="name">Patient Name:</label>
                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="address">Address:</label>
                <textarea id="address" name="address" required><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
            </div>

            <div class="form-group">
                <label for="birthdate">Birth Date:</label>
                <input type="date" id="birthdate" name="birthdate" value="<?php echo htmlspecialchars($_POST['birthdate'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="hospital_number">Hospital Number:</label>
                <input type="text" id="hospital_number" name="hospital_number" value="<?php echo htmlspecialchars($_POST['hospital_number'] ?? ''); ?>" placeholder="e.g., H123456" required>
                <small style="color: #7f8c8d;">Only letters and numbers allowed</small>
            </div>

            <button type="submit" class="btn">🔄 Generate Barcode</button>
        </form>

        <?php if (!empty($patient_data) && !empty($barcode_html)): ?>
            <div class="result">
                <div class="patient-info">
                    <h3>📋 Patient Information</h3>
                    <div class="info-row">
                        <span class="info-label">Name:</span>
                        <?php echo htmlspecialchars($patient_data['name']); ?>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Address:</span>
                        <?php echo nl2br(htmlspecialchars($patient_data['address'])); ?>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Birth Date:</span>
                        <?php echo htmlspecialchars(date('F j, Y', strtotime($patient_data['birthdate']))); ?>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Hospital Number:</span>
                        <?php echo htmlspecialchars($patient_data['hospital_number']); ?>
                    </div>
                </div>

                <div class="barcode-section">
                    <h3>📊 Generated Barcode</h3>
                    <?php echo $barcode_html; ?>
                    <br>
                    <button onclick="window.print()" class="btn print-btn">🖨️ Print</button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>